# Makefile for JAST Partner Project

# 获取当前用户的UID和GID
HOST_UID := $(shell id -u)
HOST_GID := $(shell id -g)

# 检查是否为root用户，如果是，使用默认值1000
ifeq ($(HOST_UID),0)
    $(warning 警告: 您正在以root用户运行。将使用默认UID/GID (1000)。)
    HOST_UID := 1000
    HOST_GID := 1000
endif

# 默认环境
ENV ?= development

# 默认目标
.PHONY: help
help:
	@echo "使用说明:"
	@echo "  make init [ENV=环境名]   - 初始化项目（首次克隆后运行）"
	@echo "  make build [ENV=环境名]  - 构建Docker容器"
	@echo "  make rebuild [ENV=环境名] - 清理并重新构建Docker容器"
	@echo "  make up [ENV=环境名]     - 启动所有容器 (默认: development)"
	@echo "  make down               - 停止所有容器"
	@echo "  make restart [ENV=环境名] - 重启所有容器"
	@echo "  make composer-install [ENV=环境名] - 安装Composer依赖"
	@echo "  make composer cmd=命令 [ENV=环境名] - 运行任意Composer命令"
	@echo "  make artisan cmd=命令 [ENV=环境名]  - 运行任意Artisan命令"
	@echo "  make migrate [ENV=环境名] - 运行数据库迁移"
	@echo "  make fresh [ENV=环境名]   - 重置数据库并运行所有迁移"
	@echo "  make seed [ENV=环境名]    - 运行数据库填充"
	@echo "  make test [ENV=环境名]    - 运行测试"
	@echo "  make shell [ENV=环境名]   - 进入应用容器的Shell"
	@echo "  make fix-permissions     - 修复项目文件权限"
	@echo ""
	@echo "环境选项:"
	@echo "  ENV=development (默认)"
	@echo "  ENV=production"
	@echo "  ENV=testing"

# 初始化项目
.PHONY: init
init:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	@$(MAKE) build ENV=$(ENV)
	@$(MAKE) create-project ENV=$(ENV)
	@$(MAKE) composer-install ENV=$(ENV)
	@$(MAKE) fix-permissions

# 创建Laravel项目
.PHONY: create-project
create-project:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	@echo "在已有目录中初始化Laravel项目..."
	@if [ -f "composer.json" ]; then \
		echo "composer.json已存在，跳过创建项目步骤"; \
	else \
		echo "创建临时目录..."; \
		HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash -c "mkdir -p /tmp/laravel-temp && composer create-project laravel/laravel /tmp/laravel-temp && cp -r /tmp/laravel-temp/. /var/www/ && rm -rf /tmp/laravel-temp"; \
	fi

# 构建容器
.PHONY: build
build:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose build

# 清理并重新构建容器
.PHONY: rebuild
rebuild:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	docker compose down --rmi local
	docker compose rm -f
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose build --no-cache

# 启动容器
.PHONY: up
up:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose up -d

# 停止容器
.PHONY: down
down:
	docker compose down

# 重启容器
.PHONY: restart
restart: down up

# 安装Composer依赖
.PHONY: composer-install
composer-install:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app composer install

# 运行任意Composer命令
.PHONY: composer
composer:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app composer $(cmd)

# 运行任意Artisan命令
.PHONY: artisan
artisan:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app php artisan $(cmd)

# 运行数据库迁移
.PHONY: migrate
migrate:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app php artisan migrate

# 重置数据库并运行所有迁移
.PHONY: fresh
fresh:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app php artisan migrate:fresh

# 运行数据库填充
.PHONY: seed
seed:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app php artisan db:seed

# 运行测试
.PHONY: test
test:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app php artisan test

# 进入应用容器的Shell
.PHONY: shell
shell:
	@echo "使用环境: $(ENV)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "错误: .env.$(ENV) 文件不存在"; \
		exit 1; \
	fi
	@echo "复制 .env.$(ENV) 到 .env..."
	@cp -f .env.$(ENV) .env
	HOST_UID=$(HOST_UID) HOST_GID=$(HOST_GID) docker compose run --rm app bash

# 修复项目文件权限
.PHONY: fix-permissions
fix-permissions:
	@echo "修复项目文件权限..."
	@if [ -d "storage" ]; then \
		echo "修复Laravel目录权限..."; \
		sudo chown -R $(HOST_UID):$(HOST_GID) .; \
		sudo chmod -R 755 .; \
		sudo chmod -R 775 storage bootstrap/cache; \
		echo "权限已修复"; \
	else \
		echo "storage目录不存在，只修复基本权限"; \
		sudo chown -R $(HOST_UID):$(HOST_GID) .; \
		sudo chmod -R 755 .; \
	fi
