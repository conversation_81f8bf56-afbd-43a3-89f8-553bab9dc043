<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class RoleManagementTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        // Create test user
        $this->user = User::factory()->create();
        $this->user->organisations()->attach($this->organisation->id);
    }

    public function test_can_create_role_for_organisation(): void
    {
        $role = $this->permissionService->createRole('test-role', 'api', $this->organisation->id);

        $this->assertInstanceOf(Role::class, $role);
        $this->assertEquals('test-role', $role->name);
        $this->assertEquals('api', $role->guard_name);
        $this->assertEquals($this->organisation->id, $role->organisation_id);
    }

    public function test_can_assign_role_to_user(): void
    {
        $role = $this->permissionService->createRole('test-role', 'api', $this->organisation->id);

        $this->permissionService->assignRoleToUser($this->user, $role);

        $this->assertTrue($this->user->hasRole($role, 'api', $this->organisation->id));
    }

    public function test_can_remove_role_from_user(): void
    {
        $role = $this->permissionService->createRole('test-role', 'api', $this->organisation->id);

        $this->permissionService->assignRoleToUser($this->user, $role);
        $this->assertTrue($this->user->hasRole($role, 'api', $this->organisation->id));

        $this->permissionService->removeRoleFromUser($this->user, $role);
        $this->assertFalse($this->user->hasRole($role, 'api', $this->organisation->id));
    }

    public function test_can_sync_user_roles(): void
    {
        $role1 = $this->permissionService->createRole('role-1', 'api', $this->organisation->id);
        $role2 = $this->permissionService->createRole('role-2', 'api', $this->organisation->id);

        $this->permissionService->syncUserRoles($this->user, ['role-1', 'role-2'], $this->organisation->id);

        $this->assertTrue($this->user->hasRole('role-1', 'api', $this->organisation->id));
        $this->assertTrue($this->user->hasRole('role-2', 'api', $this->organisation->id));
    }

    public function test_can_check_user_has_role(): void
    {
        $role = $this->permissionService->createRole('admin', 'api', $this->organisation->id);
        $this->permissionService->assignRoleToUser($this->user, $role);

        // Set team context and check role directly
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->user->hasRole('admin'));
    }

    public function test_can_get_user_roles(): void
    {
        $role1 = $this->permissionService->createRole('admin', 'api', $this->organisation->id);
        $role2 = $this->permissionService->createRole('manager', 'api', $this->organisation->id);

        $this->permissionService->assignRoleToUser($this->user, $role1);
        $this->permissionService->assignRoleToUser($this->user, $role2);

        $userRoles = $this->permissionService->getUserRoles($this->user);

        $this->assertCount(2, $userRoles);
        $this->assertTrue($userRoles->contains('admin'));
        $this->assertTrue($userRoles->contains('manager'));
    }

    public function test_can_get_organisation_roles(): void
    {
        $this->permissionService->createRole('admin', 'api', $this->organisation->id);
        $this->permissionService->createRole('manager', 'api', $this->organisation->id);

        $roles = $this->permissionService->getOrganisationRoles($this->organisation->id);

        $this->assertCount(2, $roles);
    }

    public function test_role_api_endpoints_require_authentication(): void
    {
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(401);
    }

    public function test_can_list_roles_with_admin_role(): void
    {
        // Create system admin role and assign to user
        $systemRoles = $this->permissionService->createSystemRoles();
        $adminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Set team context to null for system roles and assign role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->user->guard_name = 'system';
        $this->user->assignRole($adminRole);

        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data',
                     'message',
                     'timestamp'
                 ]);
    }

    public function test_cannot_list_roles_without_admin_role(): void
    {
        // Create member role and assign to user (not admin)
        $memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);
        $this->permissionService->assignRoleToUser($this->user, $memberRole);

        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(403);
    }

    public function test_creates_default_roles_for_organisation(): void
    {
        $roles = $this->permissionService->createDefaultRoles($this->organisation->id);

        $this->assertCount(2, $roles);

        $roleNames = collect($roles)->pluck('name')->toArray();
        $this->assertContains('owner', $roleNames);
        $this->assertContains('member', $roleNames);

        // Check all roles have correct guard_name and organisation_id
        foreach ($roles as $role) {
            $this->assertEquals('api', $role->guard_name);
            $this->assertEquals($this->organisation->id, $role->organisation_id);
        }
    }
}
