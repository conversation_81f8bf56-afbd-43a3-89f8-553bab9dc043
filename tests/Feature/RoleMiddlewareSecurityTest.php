<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class RoleMiddlewareSecurityTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private User $user;
    private Organisation $organisation;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->permissionService = app(PermissionService::class);
        $this->user = User::factory()->create();
        $this->organisation = Organisation::factory()->create();
        $this->user->organisations()->attach($this->organisation);
    }

    public function test_role_middleware_allows_system_admin_roles(): void
    {
        // Create system admin role
        $adminRole = $this->permissionService->createRole('admin', 'system', null);

        // Assign system admin role to user with proper guard context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->user->guard_name = 'system';
        $this->user->assignRole($adminRole);

        Sanctum::actingAs($this->user);

        // Test route that requires admin role
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    public function test_role_middleware_allows_system_root_roles(): void
    {
        // Create system root role
        $rootRole = $this->permissionService->createRole('root', 'system', null);

        // Assign system root role to user with proper guard context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->user->guard_name = 'system';
        $this->user->assignRole($rootRole);

        Sanctum::actingAs($this->user);

        // Test route that requires admin role (root should also work)
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);
    }

    public function test_role_middleware_allows_organization_roles(): void
    {
        // Create organization owner role
        $ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);
        
        // Assign owner role to user
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->user->assignRole($ownerRole);
        
        Sanctum::actingAs($this->user);
        
        // Create a test route that uses role middleware for owner
        $this->app['router']->get('/test-owner', function () {
            return response()->json(['success' => true]);
        })->middleware(['auth:sanctum', 'role:owner']);
        
        $response = $this->getJson('/test-owner');
        $response->assertStatus(200);
    }

    public function test_role_middleware_denies_unauthorized_access(): void
    {
        // Create member role (lower privilege)
        $memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);
        
        // Assign member role to user
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->user->assignRole($memberRole);
        
        Sanctum::actingAs($this->user);
        
        // Try to access admin-only route
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(403);
    }

    public function test_role_middleware_handles_mixed_guard_scenarios(): void
    {
        // Create both system and organization roles for the same user
        $adminRole = $this->permissionService->createRole('admin', 'system', null);
        $ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);

        // Assign system admin role with proper guard context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->user->guard_name = 'system';
        $this->user->assignRole($adminRole);

        // Assign organization owner role with proper guard context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->user->guard_name = 'api';
        $this->user->assignRole($ownerRole);

        Sanctum::actingAs($this->user);

        // Should be able to access admin routes (system role)
        $response = $this->getJson('/api/v1/roles');
        $response->assertStatus(200);

        // Should also be able to access owner routes (organization role)
        $this->app['router']->get('/test-owner-mixed', function () {
            return response()->json(['success' => true]);
        })->middleware(['auth:sanctum', 'role:owner']);

        $response = $this->getJson('/test-owner-mixed');
        $response->assertStatus(200);
    }
}
