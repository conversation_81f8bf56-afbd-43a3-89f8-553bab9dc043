<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;

/**
 * Example Test for API-only application
 */
final class ExampleTest extends TestCase
{
    /**
     * Test that the API health endpoint returns a successful response
     */
    public function test_the_application_returns_a_successful_response(): void
    {
        $response = $this->getJson('/api/health');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'timestamp',
                 ]);
    }

    /**
     * Test that web routes are disabled (should return 404 or redirect to API)
     */
    public function test_web_routes_are_disabled(): void
    {
        $response = $this->get('/');

        // Since we removed web routes, this should return 404
        $response->assertStatus(404);
    }
}
