<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class SimpleUserRoleTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;

    // Roles
    private Role $rootRole;
    private Role $memberRole;

    // Test users
    private User $rootUser;
    private User $memberUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create();

        // Create system and organisation roles
        $this->permissionService->createSystemRoles();
        $this->permissionService->createDefaultRoles($this->organisation->id);

        // Get roles
        $this->rootRole = Role::where('name', 'root')
            ->where('guard_name', 'system')
            ->whereNull('organisation_id')
            ->first();

        $this->memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        // Create test users
        $this->createTestUsers();
    }

    private function createTestUsers(): void
    {
        // Create system root user
        $this->rootUser = User::factory()->create();

        // Create organisation member user
        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        // Assign system role to root user
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($this->rootRole);

        // Assign organisation role to member user
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->memberUser->assignRole($this->memberRole);
    }

    public function test_permission_service_can_get_user_role_names(): void
    {
        // Test the method with pre-configured member user
        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($this->memberUser);

        $this->assertIsArray($roleInfo);
        $this->assertArrayHasKey('system_roles', $roleInfo);
        $this->assertArrayHasKey('organisation_roles', $roleInfo);
        $this->assertArrayHasKey('all_role_names', $roleInfo);

        // Should have one organisation role
        $this->assertCount(1, $roleInfo['organisation_roles']);
        $this->assertEquals('member', $roleInfo['organisation_roles'][0]['name']);
    }

    public function test_user_endpoint_returns_role_info(): void
    {
        // Test the endpoint with pre-configured member user
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/user');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'organisations',
                    'roles' => [
                        'system_roles',
                        'organisation_roles',
                        'all_role_names',
                    ],
                ],
            ]);

        $data = $response->json('data');
        $this->assertEquals($this->memberUser->id, $data['id']);
        $this->assertCount(1, $data['roles']['organisation_roles']);
    }

    public function test_can_check_role_assignment_permissions(): void
    {
        // Test permissions with pre-configured users
        $this->assertTrue($this->permissionService->canAssignRole($this->rootUser, $this->memberUser, $this->memberRole));
        $this->assertFalse($this->permissionService->canAssignRole($this->memberUser, $this->rootUser, $this->rootRole));
    }
}
