<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class UserPermissionMethodsTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;

    // System roles
    private Role $systemRootRole;
    private Role $systemAdminRole;

    // Organisation roles
    private Role $ownerRole;
    private Role $memberRole;

    // Test users
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->systemRootRole = collect($systemRoles)->firstWhere('name', 'root');
        $this->systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $this->ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->memberRole = collect($orgRoles)->firstWhere('name', 'member');

        // Create test users and assign roles
        $this->createTestUsers();
    }

    private function createTestUsers(): void
    {
        // Create system users (no organisation)
        $this->systemRootUser = User::factory()->create();
        $this->systemAdminUser = User::factory()->create();

        // Create organisation users
        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->guard_name = 'system';
        $this->systemRootUser->assignRole($this->systemRootRole);

        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Assign organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);
    }

    // ========================================
    // System-level Role Tests
    // ========================================

    public function test_system_root_has_system_admin_access(): void
    {
        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        $this->assertTrue($this->systemRootUser->hasSystemAdminAccess());
        $this->assertTrue($this->systemRootUser->isSystemRoot());
        $this->assertFalse($this->systemRootUser->isSystemAdmin());
    }

    public function test_system_admin_has_system_admin_access(): void
    {
        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        $this->assertTrue($this->systemAdminUser->hasSystemAdminAccess());
        $this->assertFalse($this->systemAdminUser->isSystemRoot());
        $this->assertTrue($this->systemAdminUser->isSystemAdmin());
    }

    // ========================================
    // Organisation-level Role Tests
    // ========================================

    public function test_organisation_member_does_not_have_system_admin_access(): void
    {
        $this->assertFalse($this->memberUser->hasSystemAdminAccess());
        $this->assertFalse($this->memberUser->isSystemRoot());
        $this->assertFalse($this->memberUser->isSystemAdmin());
    }

    public function test_organisation_owner_has_organisation_admin_access(): void
    {
        $this->assertTrue($this->ownerUser->hasOrganisationAdminAccess($this->organisation->id));
    }

    public function test_organisation_member_does_not_have_organisation_admin_access(): void
    {
        $this->assertFalse($this->memberUser->hasOrganisationAdminAccess($this->organisation->id));
    }



    // ========================================
    // Cross-Organisation Access Tests
    // ========================================

    public function test_system_admin_can_access_different_organisation(): void
    {
        // Create another organisation
        $anotherOrg = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        // System admin should have access to any organisation
        $this->assertTrue($this->systemAdminUser->hasSystemAdminAccess());
        $this->assertTrue($this->systemAdminUser->hasOrganisationAdminAccess($this->organisation->id));
        $this->assertTrue($this->systemAdminUser->hasOrganisationAdminAccess($anotherOrg->id));
    }

    public function test_organisation_owner_cannot_access_different_organisation(): void
    {
        // Create another organisation
        $anotherOrg = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Organisation owner should only have access to their own organisation
        $this->assertFalse($this->ownerUser->hasSystemAdminAccess());
        $this->assertTrue($this->ownerUser->hasOrganisationAdminAccess($this->organisation->id));
        $this->assertFalse($this->ownerUser->hasOrganisationAdminAccess($anotherOrg->id));
    }
}
