<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class UserRoleManagementTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;
    private User $rootUser;
    private User $adminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->permissionService = app(PermissionService::class);
        
        // Create test organisation
        $this->organisation = Organisation::factory()->create();
        
        // Create system roles
        $this->permissionService->createSystemRoles();
        
        // Create organisation roles
        $this->permissionService->createDefaultRoles($this->organisation->id);
        
        // Create test users
        $this->rootUser = User::factory()->create();
        $this->adminUser = User::factory()->create();

        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        $this->regularUser = User::factory()->create();
        $this->regularUser->organisations()->attach($this->organisation->id);
        
        // Assign roles
        $rootRole = Role::where('name', 'root')->where('guard_name', 'system')->whereNull('organisation_id')->first();
        $adminRole = Role::where('name', 'admin')->where('guard_name', 'system')->whereNull('organisation_id')->first();
        $ownerRole = Role::where('name', 'owner')->where('organisation_id', $this->organisation->id)->first();
        $memberRole = Role::where('name', 'member')->where('organisation_id', $this->organisation->id)->first();
        
        // Clear team context for system roles and set guard
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($rootRole);
        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($adminRole);
        
        // Set team context for organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($ownerRole);
        $this->memberUser->assignRole($memberRole);
    }

    public function test_root_can_assign_any_role(): void
    {
        Sanctum::actingAs($this->rootUser);
        
        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();
        
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '角色赋予成功',
            ]);
    }

    public function test_admin_can_assign_organisation_roles(): void
    {
        Sanctum::actingAs($this->adminUser);
        
        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();
        
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '角色赋予成功',
            ]);
    }

    public function test_owner_can_assign_member_role_only(): void
    {
        Sanctum::actingAs($this->ownerUser);
        
        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();
        
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '角色赋予成功',
            ]);
    }

    public function test_owner_cannot_assign_owner_role(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $ownerRole = Role::where('name', 'owner')
            ->where('organisation_id', $this->organisation->id)
            ->first();

        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $ownerRole->id,
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => '角色赋予失败',
            ]);
    }

    public function test_member_cannot_assign_any_role(): void
    {
        Sanctum::actingAs($this->memberUser);
        
        $memberRole = Role::where('name', 'member')
            ->where('organisation_id', $this->organisation->id)
            ->first();
        
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $memberRole->id,
        ]);
        
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => '角色赋予失败',
            ]);
    }

    public function test_cannot_assign_duplicate_owner_role(): void
    {
        Sanctum::actingAs($this->rootUser);
        
        $ownerRole = Role::where('name', 'owner')
            ->where('organisation_id', $this->organisation->id)
            ->first();
        
        $response = $this->postJson("/api/v1/users/{$this->regularUser->id}/roles", [
            'role_id' => $ownerRole->id,
        ]);
        
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => '角色赋予失败',
                'errors' => [
                    'role' => ['该组织已经有一个所有者，请先转移所有者角色。'],
                ],
            ]);
    }

    public function test_owner_role_transfer(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Ensure regularUser belongs to the organisation
        $this->regularUser->refresh();
        $this->assertTrue($this->regularUser->organisations->contains($this->organisation));

        $response = $this->putJson("/api/v1/users/{$this->regularUser->id}/transfer-owner", [
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '所有者角色转移成功',
            ]);

        // Verify the transfer
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertFalse($this->ownerUser->fresh()->hasRole('owner'));
        $this->assertTrue($this->regularUser->fresh()->hasRole('owner'));
    }

    public function test_get_user_roles_with_complete_info(): void
    {
        Sanctum::actingAs($this->rootUser);
        
        $response = $this->getJson("/api/v1/user");
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'organisations',
                    'roles' => [
                        'system_roles',
                        'organisation_roles',
                        'all_role_names',
                    ],
                ],
            ]);
    }

    public function test_get_assignable_roles(): void
    {
        Sanctum::actingAs($this->ownerUser);
        
        $response = $this->getJson('/api/v1/users/assignable-roles');
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '可赋予角色列表获取成功',
            ]);
        
        // Owner should only be able to assign member roles
        $roles = $response->json('data');
        $this->assertCount(1, $roles);
        $this->assertEquals('member', $roles[0]['name']);
    }
}
