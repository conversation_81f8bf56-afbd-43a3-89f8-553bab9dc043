<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\Role;
use App\Models\User;
use App\Models\Organisation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserOrganisationAccessTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organisation $organisation1;
    private Organisation $organisation2;
    private Role $orgRole;
    private Role $systemRole;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->organisation1 = Organisation::factory()->create();
        $this->organisation2 = Organisation::factory()->create();

        // Create roles
        $this->orgRole = Role::create([
            'name' => 'test-org-role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation1->id,
        ]);

        $this->systemRole = Role::create([
            'name' => 'test-system-role',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Attach user to organisation1
        $this->user->organisations()->attach($this->organisation1->id);
    }

    public function test_user_belongs_to_organisation()
    {
        $this->assertTrue($this->user->belongsToOrganisation($this->organisation1->id));
        $this->assertFalse($this->user->belongsToOrganisation($this->organisation2->id));
    }

    public function test_user_can_access_organisation_role()
    {
        $this->assertTrue($this->user->canAccessRole($this->orgRole));
    }

    public function test_user_cannot_access_organisation_role_without_membership()
    {
        $otherOrgRole = Role::create([
            'name' => 'other-org-role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation2->id,
        ]);

        $this->assertFalse($this->user->canAccessRole($otherOrgRole));
    }

    public function test_user_cannot_access_system_role_without_admin_access()
    {
        $this->assertFalse($this->user->canAccessRole($this->systemRole));
    }

    public function test_system_admin_can_access_system_role()
    {
        // Create system admin role and assign to user
        $adminRole = Role::create([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $this->user->assignRole($adminRole);

        $this->assertTrue($this->user->canAccessRole($this->systemRole));
    }

    public function test_user_can_assign_role_to_user_in_same_organisation()
    {
        $targetUser = User::factory()->create();
        $targetUser->organisations()->attach($this->organisation1->id);

        $this->assertTrue($this->user->canAssignRoleToUser($this->orgRole, $targetUser));
    }

    public function test_user_cannot_assign_role_to_user_in_different_organisation()
    {
        $targetUser = User::factory()->create();
        $targetUser->organisations()->attach($this->organisation2->id);

        $this->assertFalse($this->user->canAssignRoleToUser($this->orgRole, $targetUser));
    }
}
