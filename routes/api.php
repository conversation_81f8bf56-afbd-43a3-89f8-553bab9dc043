<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\StatusController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Global health check endpoint (outside versioning)
Route::get('/health', [StatusController::class, 'health']);

// API version 1 routes
Route::prefix('v1')->name('api.v1.')->group(function () {
    // Public API routes
    Route::get('/status', [StatusController::class, 'index'])->name('status');
    Route::get('/health', [StatusController::class, 'health'])->name('health');

    // Authentication routes (protected by Sanctum)
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user', function (Request $request) {
            return response()->json([
                'success' => true,
                'data' => $request->user(),
                'timestamp' => now()->toISOString(),
            ]);
        })->name('user');
    });

    // Add more API routes here as needed
    // Example: Route::apiResource('users', UserController::class);
});
