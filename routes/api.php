<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\OrganisationController;
use App\Http\Controllers\Api\V1\StatusController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\RoleController;
use App\Http\Controllers\Api\UserRoleController;
use App\Services\PermissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Global health check endpoint (outside versioning)
Route::get('/health', [StatusController::class, 'health']);

// API version 1 routes
Route::prefix('v1')->name('api.v1.')->group(function () {
    // Public API routes
    Route::get('/status', [StatusController::class, 'index'])->name('status');
    Route::get('/health', [StatusController::class, 'health'])->name('health');

    // Authentication routes (public)
    Route::prefix('auth')->name('auth.')->group(function () {
        Route::post('/login', [AuthController::class, 'login'])->name('login');
    });

    // Protected routes (require authentication)
    Route::middleware('auth:sanctum')->group(function () {
        // User information route with roles
        Route::get('/user', function (Request $request) {
            $user = $request->user();
            $permissionService = app(PermissionService::class);
            $roleInfo = $permissionService->getUserCompleteRoleInfo($user);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'email_verified_at' => $user->email_verified_at,
                    'organisations' => $user->organisations->map(function ($org) {
                        return [
                            'id' => $org->id,
                            'name' => $org->name,
                            'code' => $org->code,
                            'status' => $org->status,
                        ];
                    }),
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                    'roles' => $roleInfo,
                ],
                'timestamp' => now()->toISOString(),
            ]);
        })->name('user');

        // Authentication management routes
        Route::prefix('auth')->name('auth.')->group(function () {
            Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
            Route::get('/me', [AuthController::class, 'me'])->name('me');
            Route::post('/revoke-all-tokens', [AuthController::class, 'revokeAllTokens'])->name('revoke-all-tokens');
        });

        // Organisation management routes
        Route::apiResource('organisations', OrganisationController::class)->except(['destroy']);
        Route::post('/organisations/{id}/suspend', [OrganisationController::class, 'suspend'])->name('organisations.suspend');

        // User role management routes (must come before apiResource to avoid conflicts)
        Route::prefix('users')->name('users.')->group(function () {
            // Get assignable roles for current user
            Route::get('/assignable-roles', [UserRoleController::class, 'getAssignableRoles'])->name('assignable-roles');

            // User role operations
            Route::post('/{user}/roles', [UserRoleController::class, 'assignRole'])->name('assign-role');
            Route::delete('/{user}/roles/{role}', [UserRoleController::class, 'removeRole'])->name('remove-role');
            Route::get('/{user}/roles', [UserRoleController::class, 'getUserRoles'])->name('get-roles');

            // Owner role transfer (special case)
            Route::put('/{user}/transfer-owner', [UserRoleController::class, 'transferOwnerRole'])->name('transfer-owner');
        });

        // User management routes - restricted to admin users (Root/Admin roles)
        Route::middleware(['admin'])->group(function () {
            Route::apiResource('users', UserController::class)->except(['destroy']);
            Route::post('/users/{id}/suspend', [UserController::class, 'suspend'])->name('users.suspend');
            Route::post('/users/{id}/activate', [UserController::class, 'activate'])->name('users.activate');

            // User-Organisation association routes
            Route::post('/users/{userId}/organisations/{organisationId}', [UserController::class, 'addToOrganisation'])->name('users.add-to-organisation');
            Route::delete('/users/{userId}/organisations/{organisationId}', [UserController::class, 'removeFromOrganisation'])->name('users.remove-from-organisation');
            Route::put('/users/{userId}/organisations', [UserController::class, 'syncOrganisations'])->name('users.sync-organisations');
        });

        // Role management routes - restricted to Root and Admin roles
        Route::middleware(['admin'])->group(function () {
            Route::get('/roles', [RoleController::class, 'index'])->name('roles.index');
            Route::get('/roles/{role}', [RoleController::class, 'show'])->name('roles.show');
            Route::post('/roles', [RoleController::class, 'store'])->name('roles.store');
            Route::put('/roles/{role}', [RoleController::class, 'update'])->name('roles.update');
            Route::delete('/roles/{role}', [RoleController::class, 'destroy'])->name('roles.destroy');
        });


    });

    // Add more API routes here as needed
});
