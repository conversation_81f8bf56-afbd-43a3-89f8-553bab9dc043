<?php

declare(strict_types=1);

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

final class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, HasRoles, Notifiable;

    /**
     * The guard name for roles and permissions.
     * Note: We use multiple guards - 'api' for organisation roles and 'system' for global admin roles.
     *
     * @var string
     */
    protected $guard_name = 'api';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the organisations that the user belongs to.
     */
    public function organisations(): BelongsToMany
    {
        return $this->belongsToMany(Organisation::class, 'user_organisation')
            ->withTimestamps();
    }

    /**
     * Get the team ID for permission scoping.
     * This method is used by Spatie Permission for team-based permissions.
     * For multi-organisation users, this returns the first organisation ID.
     */
    public function getPermissionTeamId(): ?int
    {
        return $this->organisations()->first()?->id;
    }

    /**
     * Check if user has system-wide admin access (Root or Admin with system guard).
     */
    public function hasSystemAdminAccess(): bool
    {
        return $this->roles()
            ->whereIn('roles.name', ['root', 'admin'])
            ->where('roles.guard_name', 'system')
            ->exists();
    }

    /**
     * Check if user has organisation-level admin access for a specific organisation.
     */
    public function hasOrganisationAdminAccess(int $organisationId): bool
    {
        // System admins have access to all organisations
        if ($this->hasSystemAdminAccess()) {
            return true;
        }

        // Check if user belongs to the specified organisation and has admin access
        if (!$this->organisations()->where('organisations.id', $organisationId)->exists()) {
            return false;
        }

        // Only check for owner role with api guard (root/admin should only exist with system guard)
        return $this->roles()
            ->where('roles.name', 'owner')
            ->where('roles.guard_name', 'api')
            ->where('roles.organisation_id', $organisationId)
            ->exists();
    }



    /**
     * Check if user is a system root user.
     */
    public function isSystemRoot(): bool
    {
        return $this->roles()
            ->where('roles.name', 'root')
            ->where('roles.guard_name', 'system')
            ->exists();
    }

    /**
     * Check if user is a system admin user.
     */
    public function isSystemAdmin(): bool
    {
        return $this->roles()
            ->where('roles.name', 'admin')
            ->where('roles.guard_name', 'system')
            ->exists();
    }
}
