<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

final class UserRoleController extends Controller
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}

    /**
     * Assign a role to a user.
     */
    public function assignRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'role_id' => 'required|integer|exists:roles,id',
        ]);

        $role = Role::findOrFail($request->input('role_id'));
        $assigner = $request->user();

        try {
            $this->permissionService->assignRoleToUserWithValidation($assigner, $user, $role);

            return response()->json([
                'success' => true,
                'message' => '角色赋予成功',
                'data' => [
                    'user_id' => $user->id,
                    'role' => [
                        'id' => $role->id,
                        'name' => $role->name,
                        'organisation_id' => $role->organisation_id,
                    ],
                ],
                'timestamp' => now()->toISOString(),
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '角色赋予失败',
                'errors' => $e->errors(),
                'timestamp' => now()->toISOString(),
            ], 422);
        }
    }

    /**
     * Remove a role from a user.
     */
    public function removeRole(Request $request, User $user, Role $role): JsonResponse
    {
        $remover = $request->user();

        try {
            $this->permissionService->removeRoleFromUserWithValidation($remover, $user, $role);

            return response()->json([
                'success' => true,
                'message' => '角色移除成功',
                'data' => [
                    'user_id' => $user->id,
                    'role' => [
                        'id' => $role->id,
                        'name' => $role->name,
                        'organisation_id' => $role->organisation_id,
                    ],
                ],
                'timestamp' => now()->toISOString(),
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '角色移除失败',
                'errors' => $e->errors(),
                'timestamp' => now()->toISOString(),
            ], 422);
        }
    }

    /**
     * Transfer owner role from current owner to another user.
     */
    public function transferOwnerRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'organisation_id' => 'required|integer|exists:organisations,id',
        ]);

        $currentOwner = $request->user();
        $organisationId = $request->input('organisation_id');

        // The $user parameter is the new owner (from route model binding)
        $newOwner = $user;

        try {
            $this->permissionService->transferOwnerRole($currentOwner, $newOwner, $organisationId);

            return response()->json([
                'success' => true,
                'message' => '所有者角色转移成功',
                'data' => [
                    'previous_owner_id' => $currentOwner->id,
                    'new_owner_id' => $newOwner->id,
                    'organisation_id' => $organisationId,
                ],
                'timestamp' => now()->toISOString(),
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '所有者角色转移失败',
                'errors' => $e->errors(),
                'timestamp' => now()->toISOString(),
            ], 422);
        }
    }

    /**
     * Get roles that the current user can assign.
     */
    public function getAssignableRoles(Request $request): JsonResponse
    {
        $user = $request->user();
        $assignableRoles = $this->permissionService->getAssignableRoles($user);

        return response()->json([
            'success' => true,
            'data' => $assignableRoles,
            'message' => '可赋予角色列表获取成功',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get a user's roles.
     */
    public function getUserRoles(Request $request, User $user): JsonResponse
    {
        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($user);

        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_email' => $user->email,
                'organisations' => $user->organisations->map(function ($org) {
                    return [
                        'id' => $org->id,
                        'name' => $org->name,
                        'code' => $org->code,
                        'status' => $org->status,
                    ];
                }),
                'roles' => $roleInfo,
            ],
            'message' => '用户角色信息获取成功',
            'timestamp' => now()->toISOString(),
        ]);
    }
}
