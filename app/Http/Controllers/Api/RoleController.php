<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class RoleController extends Controller
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}

    /**
     * Display a listing of roles for the current user's organisation.
     * For system users (no organisation), return all system-wide roles.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        $userOrganisationIds = $user->organisations()->pluck('organisations.id');

        if ($userOrganisationIds->isNotEmpty()) {
            // User belongs to organisations, get organisation roles
            $roles = Role::whereIn('organisation_id', $userOrganisationIds)->get();
        } else {
            // System user, get system-wide roles
            $roles = Role::whereNull('organisation_id')->get();
        }

        return response()->json([
            'success' => true,
            'data' => $roles,
            'message' => 'Roles retrieved successfully',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Store a newly created role.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'guard_name' => 'sometimes|string|in:web,api',
        ]);

        $user = $request->user();

        // For multi-organisation users, use the first organisation or null for system users
        $organisationId = $user->organisations()->first()?->id;

        $role = $this->permissionService->createRole(
            $request->input('name'),
            $request->input('guard_name', 'api'),
            $organisationId // This can be null for system users
        );

        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => 'Role created successfully',
            'timestamp' => now()->toISOString(),
        ], 201);
    }

    /**
     * Display the specified role.
     */
    public function show(Request $request, Role $role): JsonResponse
    {
        $user = $request->user();

        // Ensure the role belongs to one of the user's organisations or is a system-wide role
        $userOrganisationIds = $user->organisations()->pluck('organisations.id');

        if ($userOrganisationIds->isNotEmpty()) {
            // User belongs to organisations, check organisation match
            if ($role->organisation_id === null || !$userOrganisationIds->contains($role->organisation_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Role not found',
                    'timestamp' => now()->toISOString(),
                ], 404);
            }
        } else {
            // System user, can only access system-wide roles
            if ($role->organisation_id !== null) {
                return response()->json([
                    'success' => false,
                    'message' => 'Role not found',
                    'timestamp' => now()->toISOString(),
                ], 404);
            }
        }

        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => 'Role retrieved successfully',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Update the specified role.
     * Note: This is primarily for role metadata updates, not for role assignments.
     * Role assignments should use UserRoleController.
     */
    public function update(Request $request, Role $role): JsonResponse
    {
        $user = $request->user();

        // System roles can only be updated by system admins
        if ($role->organisation_id === null) {
            if (!$user->hasSystemAdminAccess()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions to update system roles',
                    'timestamp' => now()->toISOString(),
                ], 403);
            }
        } else {
            // Organisation roles can be updated by organisation admins or system admins
            $hasSystemAccess = $user->hasSystemAdminAccess();
            $hasOrgAccess = $user->hasOrganisationAdminAccess($role->organisation_id);

            if (!$hasSystemAccess && !$hasOrgAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Role not found or insufficient permissions',
                    'timestamp' => now()->toISOString(),
                ], 404);
            }
        }

        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Prevent updating core role names
        $coreRoles = ['root', 'admin', 'owner', 'member'];
        if (in_array($role->name, $coreRoles)) {
            return response()->json([
                'success' => false,
                'message' => 'Core roles cannot be renamed',
                'timestamp' => now()->toISOString(),
            ], 422);
        }

        $role->update([
            'name' => $request->input('name'),
        ]);

        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => 'Role updated successfully',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Remove the specified role.
     */
    public function destroy(Request $request, Role $role): JsonResponse
    {
        $user = $request->user();

        // Prevent deletion of core roles
        $coreRoles = ['root', 'admin', 'owner', 'member'];
        if (in_array($role->name, $coreRoles)) {
            return response()->json([
                'success' => false,
                'message' => 'Core roles cannot be deleted',
                'timestamp' => now()->toISOString(),
            ], 422);
        }

        // System roles can only be deleted by system admins
        if ($role->organisation_id === null) {
            if (!$user->hasSystemAdminAccess()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions to delete system roles',
                    'timestamp' => now()->toISOString(),
                ], 403);
            }
        } else {
            // Organisation roles can be deleted by organisation admins or system admins
            $hasSystemAccess = $user->hasSystemAdminAccess();
            $hasOrgAccess = $user->hasOrganisationAdminAccess($role->organisation_id);

            if (!$hasSystemAccess && !$hasOrgAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Role not found or insufficient permissions',
                    'timestamp' => now()->toISOString(),
                ], 404);
            }
        }

        // Check if role is currently assigned to any users
        $usersWithRole = $role->users()->count();
        if ($usersWithRole > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete role that is currently assigned to users',
                'timestamp' => now()->toISOString(),
            ], 422);
        }

        $role->delete();

        return response()->json([
            'success' => true,
            'data' => null,
            'message' => 'Role deleted successfully',
            'timestamp' => now()->toISOString(),
        ]);
    }


}
