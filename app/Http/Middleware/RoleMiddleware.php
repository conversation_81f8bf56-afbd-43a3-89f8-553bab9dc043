<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Symfony\Component\HttpFoundation\Response;

final class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $role
     * @param  string|null  $guard
     */
    public function handle(Request $request, Closure $next, string $role, ?string $guard = null): Response
    {
        // Use the authenticated user from the request
        $user = $request->user();

        if (!$user) {
            throw UnauthorizedException::notLoggedIn();
        }

        // Determine the appropriate guard based on role type
        $targetGuard = $guard ?? $this->determineGuardForRole($role);

        $hasRole = false;

        // Check system-level roles (root, admin) with system guard
        if ($targetGuard === 'system') {
            // Clear team context for system role checks
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
            $hasRole = $user->hasRole($role, 'system');
        }
        // Check organization-level roles (owner, member) with api guard
        else if ($targetGuard === 'api') {
            // Check if user has role in any of their organisations
            $userOrganisationIds = $user->organisations()->pluck('organisations.id');

            foreach ($userOrganisationIds as $orgId) {
                app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($orgId);
                if ($user->hasRole($role, 'api')) {
                    $hasRole = true;
                    break;
                }
            }
        }
        // Handle custom guard or fallback to api guard
        else {
            // For custom guards, check both system and organization contexts
            // First check system level
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
            if ($user->hasRole($role, $targetGuard)) {
                $hasRole = true;
            } else {
                // Then check organization level
                $userOrganisationIds = $user->organisations()->pluck('organisations.id');
                foreach ($userOrganisationIds as $orgId) {
                    app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($orgId);
                    if ($user->hasRole($role, $targetGuard)) {
                        $hasRole = true;
                        break;
                    }
                }
            }
        }

        if (!$hasRole) {
            throw UnauthorizedException::forRoles([$role]);
        }

        return $next($request);
    }

    /**
     * Determine the appropriate guard for a given role.
     */
    private function determineGuardForRole(string $role): string
    {
        // System-level roles use 'system' guard
        if (in_array($role, ['root', 'admin'])) {
            return 'system';
        }

        // Organization-level roles use 'api' guard
        if (in_array($role, ['owner', 'member'])) {
            return 'api';
        }

        // Default to 'api' guard for custom roles
        return 'api';
    }
}
