<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Validation\ValidationException;

final class PermissionService
{
    /**
     * Role hierarchy levels for permission checking.
     */
    private const ROLE_HIERARCHY = [
        'root' => 4,
        'admin' => 3,
        'owner' => 2,
        'member' => 1,
    ];

    /**
     * Create a new role.
     */
    public function createRole(string $name, string $guardName = 'api', ?int $organisationId = null): Role
    {
        $role = Role::firstOrCreate([
            'name' => $name,
            'guard_name' => $guardName,
            'organisation_id' => $organisationId,
        ]);

        return $role;
    }

    /**
     * Assign role to user within organisation context.
     */
    public function assignRoleToUser(User $user, Role $role): User
    {
        // Set the team context to the role's organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);
        $user->assignRole($role);
        return $user;
    }

    /**
     * Check if a user can assign a specific role to another user.
     */
    public function canAssignRole(User $assigner, User $targetUser, Role $role): bool
    {
        // Get assigner's highest role level
        $assignerLevel = $this->getUserHighestRoleLevel($assigner);
        $targetRoleLevel = self::ROLE_HIERARCHY[$role->name] ?? 0;

        // Check basic hierarchy permission
        if ($assignerLevel <= $targetRoleLevel) {
            return false;
        }

        // Additional business rules
        return $this->validateRoleAssignmentRules($assigner, $targetUser, $role);
    }

    /**
     * Validate specific role assignment rules.
     */
    private function validateRoleAssignmentRules(User $assigner, User $targetUser, Role $role): bool
    {
        $assignerRoles = $this->getUserRoleNames($assigner);
        $roleName = $role->name;

        // Root can assign any role
        if ($assignerRoles->contains('root')) {
            return $this->validateRootAssignment($targetUser, $role);
        }

        // Admin can assign organisation-related roles
        if ($assignerRoles->contains('admin')) {
            return $this->validateAdminAssignment($targetUser, $role);
        }

        // Owner can assign member role within their organisation
        if ($assignerRoles->contains('owner')) {
            return $this->validateOwnerAssignment($assigner, $targetUser, $role);
        }

        return false;
    }

    /**
     * Validate root role assignment rules.
     */
    private function validateRootAssignment(User $targetUser, Role $role): bool
    {
        $roleName = $role->name;

        // Root and Admin roles must be global (no organisation) and use system guard
        if (in_array($roleName, ['root', 'admin'])) {
            return $role->organisation_id === null && $role->guard_name === 'system';
        }

        // Owner and Member roles must be organisation-bound and use api guard
        if (in_array($roleName, ['owner', 'member'])) {
            return $role->organisation_id !== null &&
                   $targetUser->organisations()->where('organisations.id', $role->organisation_id)->exists() &&
                   $role->guard_name === 'api';
        }

        return false;
    }

    /**
     * Validate admin role assignment rules.
     */
    private function validateAdminAssignment(User $targetUser, Role $role): bool
    {
        $roleName = $role->name;

        // Admin can only assign owner and member roles
        if (!in_array($roleName, ['owner', 'member'])) {
            return false;
        }

        // These roles must be organisation-bound
        return $role->organisation_id !== null && $targetUser->organisations()->where('organisations.id', $role->organisation_id)->exists();
    }

    /**
     * Validate owner role assignment rules.
     */
    private function validateOwnerAssignment(User $assigner, User $targetUser, Role $role): bool
    {
        $roleName = $role->name;

        // Owner can only assign member role
        if ($roleName !== 'member') {
            return false;
        }

        // Must be within the same organisation - both users must belong to the role's organisation
        return $role->organisation_id !== null &&
               $assigner->organisations()->where('organisations.id', $role->organisation_id)->exists() &&
               $targetUser->organisations()->where('organisations.id', $role->organisation_id)->exists();
    }

    /**
     * Get user's highest role level.
     */
    private function getUserHighestRoleLevel(User $user): int
    {
        $userRoles = $this->getUserRoleNames($user);
        $maxLevel = 0;

        foreach ($userRoles as $roleName) {
            $level = self::ROLE_HIERARCHY[$roleName] ?? 0;
            $maxLevel = max($maxLevel, $level);
        }

        return $maxLevel;
    }

    /**
     * Get user role names considering both system and organisation contexts.
     */
    private function getUserRoleNames(User $user): \Illuminate\Support\Collection
    {
        $allRoles = collect();

        // Get system-wide roles (clear team context first)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRoles = $user->roles()
            ->where('roles.guard_name', 'system')
            ->whereNull('roles.organisation_id')
            ->pluck('roles.name');
        $allRoles = $allRoles->merge($systemRoles);

        // Get organisation roles for all user's organisations
        $userOrganisationIds = $user->organisations()->pluck('organisations.id');

        foreach ($userOrganisationIds as $organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            $orgRoles = $user->roles()
                ->where('roles.guard_name', 'api')
                ->where('roles.organisation_id', $organisationId)
                ->pluck('roles.name');
            $allRoles = $allRoles->merge($orgRoles);
        }

        return $allRoles->unique();
    }

    /**
     * Assign role to user with validation.
     */
    public function assignRoleToUserWithValidation(User $assigner, User $targetUser, Role $role): User
    {
        // Validate assignment permission
        if (!$this->canAssignRole($assigner, $targetUser, $role)) {
            throw ValidationException::withMessages([
                'role' => ['您没有权限赋予此角色。'],
            ]);
        }

        // Special validation for owner role - only one owner per organisation
        if ($role->name === 'owner' && $role->organisation_id) {
            // Set team context for the query
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

            $existingOwner = User::whereHas('roles', function ($query) use ($role) {
                $query->where('roles.name', 'owner')
                    ->where('roles.guard_name', 'api')
                    ->where('roles.organisation_id', $role->organisation_id);
            })->where('id', '!=', $targetUser->id)->first();

            if ($existingOwner) {
                throw ValidationException::withMessages([
                    'role' => ['该组织已经有一个所有者，请先转移所有者角色。'],
                ]);
            }
        }

        // Set appropriate team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

        $targetUser->assignRole($role);
        return $targetUser;
    }

    /**
     * Check if a user can remove a specific role from another user.
     */
    public function canRemoveRole(User $remover, User $targetUser, Role $role): bool
    {
        // Users cannot remove roles from themselves
        if ($remover->id === $targetUser->id) {
            return false;
        }

        // Owner role cannot be directly removed
        if ($role->name === 'owner') {
            return false;
        }

        // Get remover's highest role level
        $removerLevel = $this->getUserHighestRoleLevel($remover);
        $targetRoleLevel = self::ROLE_HIERARCHY[$role->name] ?? 0;

        // Can only remove roles of lower level
        return $removerLevel > $targetRoleLevel;
    }

    /**
     * Remove role from user with validation.
     */
    public function removeRoleFromUserWithValidation(User $remover, User $targetUser, Role $role): User
    {
        // Validate removal permission
        if (!$this->canRemoveRole($remover, $targetUser, $role)) {
            throw ValidationException::withMessages([
                'role' => ['您没有权限移除此角色。'],
            ]);
        }

        // Set appropriate team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

        $targetUser->removeRole($role);
        return $targetUser;
    }

    /**
     * Transfer owner role from one user to another.
     */
    public function transferOwnerRole(User $currentOwner, User $newOwner, int $organisationId): bool
    {
        // Validate that current user is actually the owner
        $ownerRole = Role::where('name', 'owner')
            ->where('guard_name', 'api')
            ->where('organisation_id', $organisationId)
            ->first();

        if (!$ownerRole) {
            throw ValidationException::withMessages([
                'role' => ['组织所有者角色不存在。'],
            ]);
        }

        // Set team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);

        // Check if current owner has the role (considering they might be system admin)
        $currentOwnerHasRole = false;

        // Check if current owner is system admin (can transfer any owner role)
        $isSystemAdmin = $currentOwner->hasSystemAdminAccess();

        if ($isSystemAdmin) {
            $currentOwnerHasRole = true;
        } else {
            // Check if current owner actually has the owner role for this organisation
            $currentOwnerHasRole = $currentOwner->hasRole($ownerRole);
        }

        if (!$currentOwnerHasRole) {
            throw ValidationException::withMessages([
                'role' => ['当前用户不是该组织的所有者或系统管理员。'],
            ]);
        }

        // Verify new owner belongs to the organisation
        if (!$newOwner->organisations()->where('organisations.id', $organisationId)->exists()) {
            throw ValidationException::withMessages([
                'role' => ['新所有者必须属于指定的组织。'],
            ]);
        }

        // For current owner, they can be system admin transferring ownership
        if (!$currentOwner->hasSystemAdminAccess() && !$currentOwner->organisations()->where('organisations.id', $organisationId)->exists()) {
            throw ValidationException::withMessages([
                'role' => ['当前用户必须属于指定的组织或为系统管理员。'],
            ]);
        }

        // Remove role from current owner only if they actually have it
        if ($currentOwner->hasRole($ownerRole)) {
            $currentOwner->removeRole($ownerRole);
        }

        // Assign role to new owner
        $newOwner->assignRole($ownerRole);

        return true;
    }

    /**
     * Get available roles that a user can assign.
     */
    public function getAssignableRoles(User $user): Collection
    {
        $userRoles = $this->getUserRoleNames($user);
        $assignableRoles = collect();

        if ($userRoles->contains('root')) {
            // Root can assign all roles (both system and organisation)
            $systemRoles = Role::where('guard_name', 'system')->get();
            $orgRoles = Role::where('guard_name', 'api')->get();
            $assignableRoles = $systemRoles->merge($orgRoles);
        } elseif ($userRoles->contains('admin')) {
            // Admin can assign organisation-related roles
            $assignableRoles = Role::where('guard_name', 'api')
                ->whereIn('name', ['owner', 'member'])
                ->get();
        } elseif ($userRoles->contains('owner')) {
            // Owner can assign member role within their organisations
            $userOrganisationIds = $user->organisations()->pluck('organisations.id');
            $assignableRoles = Role::where('guard_name', 'api')
                ->where('name', 'member')
                ->whereIn('organisation_id', $userOrganisationIds)
                ->get();
        }

        return $assignableRoles;
    }

    /**
     * Remove role from user within organisation context.
     */
    public function removeRoleFromUser(User $user, Role $role): User
    {
        // Set the team context to the role's organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);
        $user->removeRole($role);
        return $user;
    }

    /**
     * Get user's complete role information including both system and organisation roles.
     */
    public function getUserCompleteRoleInfo(User $user): array
    {
        // Get system-wide roles (clear team context first)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRoles = $user->roles()
            ->where('roles.guard_name', 'system')
            ->whereNull('roles.organisation_id')
            ->get(['roles.id', 'roles.name', 'roles.organisation_id']);

        // Get organisation roles for all user's organisations
        $organisationRoles = collect();
        $userOrganisationIds = $user->organisations()->pluck('organisations.id');

        foreach ($userOrganisationIds as $organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            $orgRoles = $user->roles()
                ->where('roles.guard_name', 'api')
                ->where('roles.organisation_id', $organisationId)
                ->get(['roles.id', 'roles.name', 'roles.organisation_id']);
            $organisationRoles = $organisationRoles->merge($orgRoles);
        }

        return [
            'system_roles' => $systemRoles,
            'organisation_roles' => $organisationRoles,
            'all_role_names' => $systemRoles->pluck('name')->merge($organisationRoles->pluck('name'))->unique()->values(),
        ];
    }



    /**
     * Get all roles for a user within organisation context.
     */
    public function getUserRoles(User $user, ?int $organisationId = null): \Illuminate\Support\Collection
    {
        if ($organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            return $user->getRoleNames();
        }

        // Get roles from all user's organisations
        $allRoles = collect();
        $userOrganisationIds = $user->organisations()->pluck('organisations.id');

        foreach ($userOrganisationIds as $orgId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($orgId);
            $orgRoles = $user->getRoleNames();
            $allRoles = $allRoles->merge($orgRoles);
        }

        return $allRoles->unique();
    }



    /**
     * Get all roles for organisation.
     */
    public function getOrganisationRoles(int $organisationId): Collection
    {
        return Role::where('organisation_id', $organisationId)->get();
    }

    /**
     * Sync user roles within organisation context.
     */
    public function syncUserRoles(User $user, array $roleNames, int $organisationId): User
    {
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
        $user->syncRoles($roleNames);
        return $user;
    }





    /**
     * Create default roles for an organisation.
     * Note: Only creates organisation-specific roles (owner, member).
     * Global admin roles (root, admin) are created via createSystemRoles().
     */
    public function createDefaultRoles(int $organisationId): array
    {
        $roles = [
            'owner',          // 组织所有者
            'member',         // 成员
        ];

        $createdRoles = [];
        foreach ($roles as $roleName) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'api',
                'organisation_id' => $organisationId,
            ]);

            $createdRoles[] = $role;
        }

        return $createdRoles;
    }

    /**
     * Check if user has system admin access (simplified method).
     */
    public function hasSystemAdminAccess(User $user): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Check if user has organisation admin access for a specific organisation.
     */
    public function hasOrganisationAdminAccess(User $user, int $organisationId): bool
    {
        return $user->hasOrganisationAdminAccess($organisationId);
    }



    /**
     * Create global system roles (not tied to any organisation).
     */
    public function createSystemRoles(): array
    {
        $systemRoles = [
            'root',           // Root - 系统初始化时设置的第一个管理员账户
            'admin',          // 管理员
        ];

        $createdRoles = [];
        foreach ($systemRoles as $roleName) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'system', // Use 'system' guard for global admin roles
                'organisation_id' => null, // System-wide roles
            ]);

            $createdRoles[] = $role;
        }

        return $createdRoles;
    }
}
