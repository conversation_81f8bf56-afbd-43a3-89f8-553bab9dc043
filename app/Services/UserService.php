<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

final class UserService
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}
    /**
     * Get all users with optional filtering.
     * Requires system admin access.
     */
    public function getUsers(int $perPage = 15, ?int $organisationId = null, ?string $status = null, ?User $currentUser = null): LengthAwarePaginator
    {
        // Authorization check - only system admins can list all users
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to list users.']
            ]);
        }

        $query = User::with('organisations');

        if ($organisationId) {
            $query->whereHas('organisations', function ($q) use ($organisationId) {
                $q->where('organisations.id', $organisationId);
            });
        }

        // Add status filtering if needed (this would require adding a status column to users table)
        // For now, we'll skip status filtering as the current User model doesn't have a status field

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get users by organisation.
     */
    public function getUsersByOrganisation(int $organisationId, int $perPage = 15): LengthAwarePaginator
    {
        return User::with('organisations')
            ->whereHas('organisations', function ($query) use ($organisationId) {
                $query->where('organisations.id', $organisationId);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get user by ID.
     */
    public function getById(int $id): ?User
    {
        return User::find($id);
    }

    /**
     * Create a new user.
     * Requires system admin access.
     */
    public function create(array $data, ?User $currentUser = null): User
    {
        // Authorization check - only system admins can create users
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to create users.']
            ]);
        }

        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Extract organisation_ids before creating user
        $organisationIds = $data['organisation_ids'] ?? [];
        unset($data['organisation_ids']);

        $user = User::create($data);

        // Attach organisations if provided
        if (!empty($organisationIds)) {
            $user->organisations()->attach($organisationIds);
        }

        return $user;
    }

    /**
     * Update user.
     * Requires system admin access.
     */
    public function update(User $user, array $data, ?User $currentUser = null): User
    {
        // Authorization check - only system admins can update users
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to update users.']
            ]);
        }

        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Handle organisation_ids if provided
        if (isset($data['organisation_ids'])) {
            $this->syncOrganisations($user, $data['organisation_ids'], $currentUser);
            unset($data['organisation_ids']);
        }

        $user->update($data);
        return $user;
    }

    /**
     * Suspend user (revoke all tokens).
     * Requires system admin access.
     */
    public function suspend(User $user, ?User $currentUser = null): User
    {
        // Authorization check - only system admins can suspend users
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to suspend users.']
            ]);
        }

        // Revoke all tokens for the user
        $user->tokens()->delete();

        return $user;
    }

    /**
     * Activate user.
     * Requires system admin access.
     */
    public function activate(User $user, ?User $currentUser = null): User
    {
        // Authorization check - only system admins can activate users
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to activate users.']
            ]);
        }

        // For now, this is a placeholder
        // In the future, you might want to add a status field to users table
        return $user;
    }

    /**
     * Get users count by organisation.
     */
    public function getUsersCountByOrganisation(int $organisationId): int
    {
        return User::whereHas('organisations', function ($query) use ($organisationId) {
            $query->where('organisations.id', $organisationId);
        })->count();
    }

    /**
     * Search users by name or email.
     */
    public function searchUsers(string $search, int $perPage = 15, ?int $organisationId = null): LengthAwarePaginator
    {
        $query = User::with('organisations')
            ->where(function (Builder $query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });

        if ($organisationId) {
            $query->whereHas('organisations', function ($query) use ($organisationId) {
                $query->where('organisations.id', $organisationId);
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Add user to organisation.
     * Requires system admin access.
     */
    public function addToOrganisation(User $user, int $organisationId, ?User $currentUser = null): User
    {
        // Authorization check - only system admins can manage user-organisation relationships
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to manage user-organisation relationships.']
            ]);
        }

        if (!$user->organisations()->where('organisations.id', $organisationId)->exists()) {
            $user->organisations()->attach($organisationId);
        }

        return $user;
    }

    /**
     * Remove user from organisation.
     * Requires system admin access.
     */
    public function removeFromOrganisation(User $user, int $organisationId, ?User $currentUser = null): User
    {
        // Authorization check - only system admins can manage user-organisation relationships
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to manage user-organisation relationships.']
            ]);
        }

        // Remove all organisation-specific roles before removing from organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
        $user->syncRoles([]);

        // Remove user from organisation
        $user->organisations()->detach($organisationId);

        return $user;
    }

    /**
     * Sync user organisations.
     * Requires system admin access.
     */
    public function syncOrganisations(User $user, array $organisationIds, ?User $currentUser = null): User
    {
        // Authorization check - only system admins can manage user-organisation relationships
        if ($currentUser && !$currentUser->hasSystemAdminAccess()) {
            throw ValidationException::withMessages([
                'authorization' => ['Insufficient permissions to manage user-organisation relationships.']
            ]);
        }

        // Get current organisation IDs
        $currentOrganisationIds = $user->organisations()->pluck('organisations.id')->toArray();

        // Find organisations being removed
        $removedOrganisationIds = array_diff($currentOrganisationIds, $organisationIds);

        // Remove roles from organisations being removed
        foreach ($removedOrganisationIds as $organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            $user->syncRoles([]);
        }

        // Sync organisations
        $user->organisations()->sync($organisationIds);

        return $user;
    }
}
