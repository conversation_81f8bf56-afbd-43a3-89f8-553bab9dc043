# 角色管理改进文档

## 问题修复

### 1. 角色创建时组织分配问题

**问题描述：**
之前在 `RoleController::store()` 方法中，新创建的角色会自动分配给用户的第一个组织，这是非预期的行为。

**修复方案：**
- 添加 `organisation_id` 参数，允许用户明确指定要为哪个组织创建角色
- 添加访问权限验证，确保用户只能为自己所属的组织创建角色
- 改进系统角色和组织角色的创建逻辑

### 2. 重复代码抽离

**问题描述：**
多个文件中存在重复的组织访问检查代码：
```php
$targetUser->organisations()->where('organisations.id', $role->organisation_id)->exists()
```

**修复方案：**
在 `User` 模型中添加公共方法：
- `belongsToOrganisation(int $organisationId): bool`
- `canAccessRole(Role $role): bool`
- `canAssignRoleToUser(Role $role, User $targetUser): bool`

## API 接口变更

### 创建角色接口 (POST /api/roles)

**新增参数：**
```json
{
    "name": "角色名称",
    "guard_name": "api|system",
    "organisation_id": 1  // 可选，指定组织ID
}
```

**验证规则：**
- `organisation_id`: 可选，必须是存在的组织ID
- 如果指定了 `organisation_id`，用户必须属于该组织
- 组织角色不能使用 `system` guard
- 系统角色只能由系统管理员创建

**响应示例：**

成功创建组织角色：
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "custom-role",
        "guard_name": "api",
        "organisation_id": 1,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "message": "Role created successfully",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

错误响应 - 无权限访问组织：
```json
{
    "success": false,
    "message": "You do not have access to this organisation",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

错误响应 - 组织角色使用系统 guard：
```json
{
    "success": false,
    "message": "Organisation roles cannot use system guard",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 新增的 User 模型方法

### belongsToOrganisation(int $organisationId): bool
检查用户是否属于指定组织。

### canAccessRole(Role $role): bool
检查用户是否可以访问指定角色：
- 系统角色：需要系统管理员权限
- 组织角色：需要属于该组织

### canAssignRoleToUser(Role $role, User $targetUser): bool
检查用户是否可以将角色分配给目标用户：
- 系统角色：需要系统管理员权限
- 组织角色：分配者和目标用户都必须属于该组织

## 测试

创建了 `UserOrganisationAccessTest` 测试类来验证新功能：
- 组织成员关系检查
- 角色访问权限检查
- 角色分配权限检查

## 向后兼容性

这些修改保持了向后兼容性：
- 现有的 API 调用仍然有效
- 如果不指定 `organisation_id`，系统会根据用户权限自动处理
- 现有的权限检查逻辑得到了增强而不是替换

## 使用建议

1. **创建组织角色时**，明确指定 `organisation_id` 参数
2. **创建系统角色时**，确保用户具有系统管理员权限
3. **使用新的 User 模型方法**来替换重复的组织访问检查代码
4. **运行测试**确保修改不会影响现有功能
