ARG PHP_VERSION=8.4.7

FROM php:${PHP_VERSION}-fpm

# Set working directory
WORKDIR /var/www

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libicu-dev \
    zip \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure and install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install \
    pdo_mysql \
    mysqli \
    zip \
    exif \
    pcntl \
    bcmath \
    intl \
    gd

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Add user for application with dynamic UID/GID
ARG USER_ID=1000
ARG GROUP_ID=1000

# Skip user creation if UID/GID is 0 (root)
RUN if [ "$USER_ID" != "0" ] && [ "$GROUP_ID" != "0" ]; then \
        groupadd -g ${GROUP_ID} www 2>/dev/null || groupmod -o -g ${GROUP_ID} www 2>/dev/null || echo "Using existing group with GID ${GROUP_ID}"; \
        useradd -u ${USER_ID} -g ${GROUP_ID} -m -s /bin/bash www 2>/dev/null || usermod -o -u ${USER_ID} -g ${GROUP_ID} www 2>/dev/null || echo "Using existing user with UID ${USER_ID}"; \
        chown -R ${USER_ID}:${GROUP_ID} /var/www; \
    else \
        echo "Running as root user"; \
    fi

# Expose port 9000
EXPOSE 9000

# Set default user based on USER_ID
USER ${USER_ID:-1000}

CMD ["php-fpm"]
